<script setup lang="ts">
import { useBackHistory } from '@/hooks/useCommon.ts'
const { t } = useI18n()

const keyword = ref('')
const getDressList = () => {
  console.log(keyword.value)
}
</script>

<template>
  <div class="skin-container">
    <div class="top-bar flex-center-center">
      <IconSvgLeftArrow
        class="fsize-24 back"
        @click="useBackHistory"
      />
      <div>{{ t('我的图鉴') }}</div>
    </div>

    <van-field
      @keydown.enter="getDressList(keyword)"
      class="search-bar"
      v-model="keyword"
      clearable
      clear-icon="close"
      :placeholder="t('searchAgentName')"
      @clear="getDressList()"
    >
      <template #left-icon>
        <SvgIcon
          @click="getDressList(keyword)"
          icon-class="blur-search"
          class="fsize-18"
      /></template>
    </van-field>

    <div class="agent-tag">
      <div class="agent-item active">全部</div>
      <div
        v-for="item in 10"
        :key="item"
        class="agent-item"
      >
        Rin {{ item }}
      </div>
    </div>

    <div class="agent-skin">
      <div class="series-container">
        <div class="flex-between-center mb-16">
          <div class="series-title">校园日常(4/6)</div>
          <div class="gift">1</div>
        </div>
        <div class="series-skins">
          <div
            v-for="item in 10"
            :key="item"
            class="series-skin lock"
          >
            <img
              src="@/assets/images/mine/49b66793ce076fe5a88ddf758359650563fc73b7.png"
              alt=""
              class="skin"
            />
            <img
              src="@/assets/images/skin/skin-top-mask.png"
              alt="mask"
              class="mask-top"
            />
            <div class="mask-text"></div>
            <img
              src="@/assets/images/skin/skin-bottom-mask.png"
              alt="mask"
              class="mask-bottom"
            />
            <div class="skin-describe">This is skin describe 1 2 但是多少度是的是的</div>
            <div class="overlay-radius"></div>
            <div class="lock">
              <IconSvgStoryLock class="fsize-24" />
              <div>未获得</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
@import '@/assets/styles/mixin';
.skin-container {
  padding: 0 8px;
  display: flex;
  flex-direction: column;
  height: 100%;
  background: url('@/assets/images/skin/bg.png') no-repeat top / contain;

  .top-bar {
    width: 100%;
    padding: 16px 12px;

    .back {
      position: absolute;
      left: 12px;
    }
  }

  .search-bar {
    display: flex;
    flex-shrink: 0;
    width: 100%;
    padding: 8px 16px;
    background: #232222;
    border-radius: 12px;

    :deep(.van-field__left-icon) {
      display: flex;
      align-items: center;
      margin-right: 12px;
    }
  }

  .agent-tag {
    display: flex;
    flex-wrap: nowrap;
    gap: 8px 8px;
    margin-top: 16px;
    margin-bottom: 22px;
    overflow-x: auto;

    .agent-item {
      flex-shrink: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 6px 12px;
      font-size: 14px;
      color: #fff;
      background: rgba(255, 255, 255, 10%);
      border-radius: 10px;

      &.active {
        background: #ebdfac29;
        color: #ebdfac;
      }
    }
  }

  .agent-skin {
    flex: 1;
    overflow-y: auto;
  }

  .series-skins {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
  }

  .series-skin {
    overflow: hidden;
    position: relative;
    @include gradation-border-with-radius-and-transparent-bg(1px, linear-gradient(161.44deg, #fff4db -0.56%, #ffdd91 100%));
    border-radius: 24px 0 24px 0;

    .overlay-radius {
      @include gradation-border-with-radius-and-transparent-bg(1px, linear-gradient(161.44deg, #fff4db -0.56%, #ffdd91 100%));

      position: absolute;
      z-index: 1;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border-radius: 24px 16px;
    }

    &.lock {
      opacity: 0.3;
    }

    .lock {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 4px;
      color: #fff4db;
      font-size: 10px;
    }

    .mask-top {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
    }

    .mask-bottom {
      position: absolute;
      bottom: 0;
      right: 0;
      width: 100%;
    }

    .mask-text {
      position: absolute;
      border-radius: 0 20px;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 24%;
      background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.6) 29.85%, #000000 100%);
    }

    .skin-describe {
      position: absolute;
      bottom: 8px;
      left: 0;
      width: 100%;
      padding: 0 8px;
      font-size: 10px;
      color: #fff;
    }

    .skin {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
}
</style>
